/**
 * Vibrant emotion-to-color mapping for all 48 Hume emotions
 * Designed for engaging, dynamic UI/UX with high emotional intensity visualization
 */

export interface EmotionColorScheme {
  primary: string;    // Main RGB color
  secondary: string;  // Secondary RGB color
  accent: string;     // Accent RGB color
  glow: string;       // Glow effect with RGBA
  intensity: number;  // 0.0 - 1.0
  vibrance: number;   // 0.0 - 1.0
}

// Complete mapping for all 48 Hume emotions with vibrant, engaging colors
export const HUME_EMOTION_COLORS: Record<string, EmotionColorScheme> = {
  // Positive High-Energy Emotions - Vibrant warm colors
  admiration: {
    primary: 'rgb(255, 140, 0)', // Dark orange
    secondary: 'rgb(255, 165, 0)', // Orange
    accent: 'rgb(255, 215, 0)', // Gold
    glow: 'rgba(255, 140, 0, 0.8)', // Vibrant orange glow
    intensity: 0.9,
    vibrance: 0.95
  },

  adoration: {
    primary: 'rgb(255, 20, 147)', // Deep pink
    secondary: 'rgb(255, 105, 180)', // Hot pink
    accent: 'rgb(255, 182, 193)', // Light pink
    glow: 'rgba(255, 20, 147, 0.9)', // Deep pink glow
    intensity: 0.95,
    vibrance: 1.0
  },

  aestheticAppreciation: {
    primary: 'rgb(138, 43, 226)', // Blue violet
    secondary: 'rgb(147, 112, 219)', // Medium purple
    accent: 'rgb(186, 85, 211)', // Medium orchid
    glow: 'rgba(138, 43, 226, 0.8)', // Blue violet glow
    intensity: 0.85,
    vibrance: 0.9
  },

  amusement: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 255, 0)', // Yellow
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 215, 0, 0.8)', // Gold glow
    intensity: 0.9,
    vibrance: 0.95
  },

  joy: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 255, 0)', // Yellow
    accent: 'rgb(255, 140, 0)', // Dark orange
    glow: 'rgba(255, 215, 0, 0.9)', // Bright gold glow
    intensity: 0.95,
    vibrance: 1.0
  },

  excitement: {
    primary: 'rgb(255, 69, 0)', // Orange red
    secondary: 'rgb(255, 140, 0)', // Dark orange
    accent: 'rgb(255, 165, 0)', // Orange
    glow: 'rgba(255, 69, 0, 0.9)', // Vibrant orange red glow
    intensity: 1.0,
    vibrance: 1.0
  },

  ecstasy: {
    primary: 'rgb(255, 20, 147)', // Deep pink
    secondary: 'rgb(255, 0, 255)', // Magenta
    accent: 'rgb(255, 105, 180)', // Hot pink
    glow: 'rgba(255, 20, 147, 1.0)', // Maximum deep pink glow
    intensity: 1.0,
    vibrance: 1.0
  },

  triumph: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 140, 0)', // Dark orange
    accent: 'rgb(255, 165, 0)', // Orange
    glow: 'rgba(255, 215, 0, 0.95)', // Brilliant gold glow
    intensity: 0.98,
    vibrance: 1.0
  },

  // Positive Medium-Energy Emotions - Vibrant medium tones
  contentment: {
    primary: 'rgb(0, 191, 255)', // Deep sky blue
    secondary: 'rgb(135, 206, 235)', // Sky blue
    accent: 'rgb(173, 216, 230)', // Light blue
    glow: 'rgba(0, 191, 255, 0.8)', // Deep sky blue glow
    intensity: 0.75,
    vibrance: 0.85
  },

  satisfaction: {
    primary: 'rgb(50, 205, 50)', // Lime green
    secondary: 'rgb(144, 238, 144)', // Light green
    accent: 'rgb(152, 251, 152)', // Pale green
    glow: 'rgba(50, 205, 50, 0.8)', // Lime green glow
    intensity: 0.8,
    vibrance: 0.9
  },

  relief: {
    primary: 'rgb(0, 255, 127)', // Spring green
    secondary: 'rgb(144, 238, 144)', // Light green
    accent: 'rgb(240, 255, 240)', // Honeydew
    glow: 'rgba(0, 255, 127, 0.7)', // Spring green glow
    intensity: 0.7,
    vibrance: 0.8
  },

  pride: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 140, 0)', // Dark orange
    accent: 'rgb(255, 165, 0)', // Orange
    glow: 'rgba(255, 215, 0, 0.85)', // Bright gold glow
    intensity: 0.9,
    vibrance: 0.95
  },

  love: {
    primary: 'rgb(255, 20, 147)', // Deep pink
    secondary: 'rgb(255, 105, 180)', // Hot pink
    accent: 'rgb(255, 182, 193)', // Light pink
    glow: 'rgba(255, 20, 147, 0.9)', // Deep pink glow
    intensity: 0.95,
    vibrance: 1.0
  },

  romance: {
    primary: 'rgb(255, 105, 180)', // Hot pink
    secondary: 'rgb(255, 20, 147)', // Deep pink
    accent: 'rgb(255, 182, 193)', // Light pink
    glow: 'rgba(255, 105, 180, 0.85)', // Hot pink glow
    intensity: 0.9,
    vibrance: 0.95
  },

  // Positive Low-Energy Emotions - Soft but vibrant
  calmness: {
    primary: 'rgb(135, 206, 235)', // Sky blue
    secondary: 'rgb(173, 216, 230)', // Light blue
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(135, 206, 235, 0.6)', // Sky blue glow
    intensity: 0.6,
    vibrance: 0.7
  },

  // Neutral/Cognitive Emotions - Cool vibrant tones
  concentration: {
    primary: 'rgb(123, 104, 238)', // Medium slate blue
    secondary: 'rgb(147, 112, 219)', // Medium purple
    accent: 'rgb(230, 230, 250)', // Lavender
    glow: 'rgba(123, 104, 238, 0.8)', // Medium slate blue glow
    intensity: 0.8,
    vibrance: 0.85
  },

  contemplation: {
    primary: 'rgb(147, 112, 219)', // Medium purple
    secondary: 'rgb(138, 43, 226)', // Blue violet
    accent: 'rgb(186, 85, 211)', // Medium orchid
    glow: 'rgba(147, 112, 219, 0.8)', // Medium purple glow
    intensity: 0.75,
    vibrance: 0.8
  },

  interest: {
    primary: 'rgb(70, 130, 180)', // Steel blue
    secondary: 'rgb(100, 149, 237)', // Cornflower blue
    accent: 'rgb(173, 216, 230)', // Light blue
    glow: 'rgba(70, 130, 180, 0.8)', // Steel blue glow
    intensity: 0.8,
    vibrance: 0.85
  },

  realization: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 255, 0)', // Yellow
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 215, 0, 0.8)', // Gold glow
    intensity: 0.85,
    vibrance: 0.9
  },

  // Negative emotions - Vibrant but controlled colors
  anger: {
    primary: 'rgb(220, 20, 60)', // Crimson
    secondary: 'rgb(255, 69, 0)', // Orange red
    accent: 'rgb(255, 99, 71)', // Tomato
    glow: 'rgba(220, 20, 60, 0.8)', // Crimson glow
    intensity: 0.85,
    vibrance: 0.9
  },

  anxiety: {
    primary: 'rgb(138, 43, 226)', // Blue violet
    secondary: 'rgb(75, 0, 130)', // Indigo
    accent: 'rgb(147, 112, 219)', // Medium purple
    glow: 'rgba(138, 43, 226, 0.7)', // Blue violet glow
    intensity: 0.7,
    vibrance: 0.8
  },

  sadness: {
    primary: 'rgb(70, 130, 180)', // Steel blue
    secondary: 'rgb(100, 149, 237)', // Cornflower blue
    accent: 'rgb(173, 216, 230)', // Light blue
    glow: 'rgba(70, 130, 180, 0.7)', // Steel blue glow
    intensity: 0.7,
    vibrance: 0.75
  },

  fear: {
    primary: 'rgb(75, 0, 130)', // Indigo
    secondary: 'rgb(72, 61, 139)', // Dark slate blue
    accent: 'rgb(123, 104, 238)', // Medium slate blue
    glow: 'rgba(75, 0, 130, 0.7)', // Indigo glow
    intensity: 0.7,
    vibrance: 0.8
  },

  // Additional emotions with vibrant colors
  confusion: {
    primary: 'rgb(255, 165, 0)', // Orange
    secondary: 'rgb(255, 140, 0)', // Dark orange
    accent: 'rgb(255, 215, 0)', // Gold
    glow: 'rgba(255, 165, 0, 0.7)', // Orange glow
    intensity: 0.7,
    vibrance: 0.8
  },

  surprise: {
    primary: 'rgb(255, 255, 0)', // Yellow
    secondary: 'rgb(255, 215, 0)', // Gold
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(255, 255, 0, 0.8)', // Bright yellow glow
    intensity: 0.85,
    vibrance: 0.9
  },

  surprisePositive: {
    primary: 'rgb(255, 215, 0)', // Gold
    secondary: 'rgb(255, 255, 0)', // Yellow
    accent: 'rgb(255, 140, 0)', // Dark orange
    glow: 'rgba(255, 215, 0, 0.85)', // Gold glow
    intensity: 0.9,
    vibrance: 0.95
  },

  surpriseNegative: {
    primary: 'rgb(123, 104, 238)', // Medium slate blue
    secondary: 'rgb(72, 61, 139)', // Dark slate blue
    accent: 'rgb(147, 112, 219)', // Medium purple
    glow: 'rgba(123, 104, 238, 0.7)', // Medium slate blue glow
    intensity: 0.7,
    vibrance: 0.8
  },

  // Additional Hume emotions with vibrant colors
  awe: {
    primary: 'rgb(138, 43, 226)', // Blue violet
    secondary: 'rgb(147, 112, 219)', // Medium purple
    accent: 'rgb(186, 85, 211)', // Medium orchid
    glow: 'rgba(138, 43, 226, 0.9)', // Blue violet glow
    intensity: 0.9,
    vibrance: 0.95
  },

  awkwardness: {
    primary: 'rgb(255, 105, 180)', // Hot pink
    secondary: 'rgb(255, 182, 193)', // Light pink
    accent: 'rgb(255, 228, 225)', // Misty rose
    glow: 'rgba(255, 105, 180, 0.6)', // Hot pink glow
    intensity: 0.6,
    vibrance: 0.7
  },

  boredom: {
    primary: 'rgb(128, 128, 128)', // Gray
    secondary: 'rgb(169, 169, 169)', // Dark gray
    accent: 'rgb(192, 192, 192)', // Silver
    glow: 'rgba(128, 128, 128, 0.5)', // Gray glow
    intensity: 0.4,
    vibrance: 0.5
  },

  contempt: {
    primary: 'rgb(128, 0, 128)', // Purple
    secondary: 'rgb(75, 0, 130)', // Indigo
    accent: 'rgb(147, 112, 219)', // Medium purple
    glow: 'rgba(128, 0, 128, 0.7)', // Purple glow
    intensity: 0.7,
    vibrance: 0.8
  },

  craving: {
    primary: 'rgb(255, 140, 0)', // Dark orange
    secondary: 'rgb(255, 165, 0)', // Orange
    accent: 'rgb(255, 215, 0)', // Gold
    glow: 'rgba(255, 140, 0, 0.8)', // Dark orange glow
    intensity: 0.8,
    vibrance: 0.85
  },

  desire: {
    primary: 'rgb(255, 20, 147)', // Deep pink
    secondary: 'rgb(255, 105, 180)', // Hot pink
    accent: 'rgb(255, 182, 193)', // Light pink
    glow: 'rgba(255, 20, 147, 0.85)', // Deep pink glow
    intensity: 0.9,
    vibrance: 0.95
  },

  determination: {
    primary: 'rgb(255, 140, 0)', // Dark orange
    secondary: 'rgb(255, 215, 0)', // Gold
    accent: 'rgb(255, 165, 0)', // Orange
    glow: 'rgba(255, 140, 0, 0.9)', // Dark orange glow
    intensity: 0.95,
    vibrance: 1.0
  },

  disappointment: {
    primary: 'rgb(70, 130, 180)', // Steel blue
    secondary: 'rgb(100, 149, 237)', // Cornflower blue
    accent: 'rgb(173, 216, 230)', // Light blue
    glow: 'rgba(70, 130, 180, 0.7)', // Steel blue glow
    intensity: 0.7,
    vibrance: 0.75
  },

  disgust: {
    primary: 'rgb(34, 139, 34)', // Forest green
    secondary: 'rgb(50, 205, 50)', // Lime green
    accent: 'rgb(144, 238, 144)', // Light green
    glow: 'rgba(34, 139, 34, 0.7)', // Forest green glow
    intensity: 0.7,
    vibrance: 0.8
  },

  distress: {
    primary: 'rgb(220, 20, 60)', // Crimson
    secondary: 'rgb(255, 69, 0)', // Orange red
    accent: 'rgb(255, 99, 71)', // Tomato
    glow: 'rgba(220, 20, 60, 0.8)', // Crimson glow
    intensity: 0.8,
    vibrance: 0.85
  },

  doubt: {
    primary: 'rgb(147, 112, 219)', // Medium purple
    secondary: 'rgb(138, 43, 226)', // Blue violet
    accent: 'rgb(186, 85, 211)', // Medium orchid
    glow: 'rgba(147, 112, 219, 0.7)', // Medium purple glow
    intensity: 0.7,
    vibrance: 0.8
  },

  embarrassment: {
    primary: 'rgb(255, 105, 180)', // Hot pink
    secondary: 'rgb(255, 182, 193)', // Light pink
    accent: 'rgb(255, 228, 225)', // Misty rose
    glow: 'rgba(255, 105, 180, 0.6)', // Hot pink glow
    intensity: 0.6,
    vibrance: 0.7
  },

  empathicPain: {
    primary: 'rgb(123, 104, 238)', // Medium slate blue
    secondary: 'rgb(147, 112, 219)', // Medium purple
    accent: 'rgb(230, 230, 250)', // Lavender
    glow: 'rgba(123, 104, 238, 0.7)', // Medium slate blue glow
    intensity: 0.7,
    vibrance: 0.75
  },

  entrancement: {
    primary: 'rgb(138, 43, 226)', // Blue violet
    secondary: 'rgb(147, 112, 219)', // Medium purple
    accent: 'rgb(186, 85, 211)', // Medium orchid
    glow: 'rgba(138, 43, 226, 0.9)', // Blue violet glow
    intensity: 0.9,
    vibrance: 0.95
  },

  envy: {
    primary: 'rgb(34, 139, 34)', // Forest green
    secondary: 'rgb(50, 205, 50)', // Lime green
    accent: 'rgb(144, 238, 144)', // Light green
    glow: 'rgba(34, 139, 34, 0.7)', // Forest green glow
    intensity: 0.7,
    vibrance: 0.8
  },

  guilt: {
    primary: 'rgb(128, 0, 128)', // Purple
    secondary: 'rgb(75, 0, 130)', // Indigo
    accent: 'rgb(147, 112, 219)', // Medium purple
    glow: 'rgba(128, 0, 128, 0.7)', // Purple glow
    intensity: 0.7,
    vibrance: 0.8
  },

  horror: {
    primary: 'rgb(25, 25, 112)', // Midnight blue
    secondary: 'rgb(72, 61, 139)', // Dark slate blue
    accent: 'rgb(75, 0, 130)', // Indigo
    glow: 'rgba(25, 25, 112, 0.8)', // Midnight blue glow
    intensity: 0.8,
    vibrance: 0.85
  },

  nostalgia: {
    primary: 'rgb(218, 165, 32)', // Goldenrod
    secondary: 'rgb(255, 215, 0)', // Gold
    accent: 'rgb(255, 239, 213)', // Papaya whip
    glow: 'rgba(218, 165, 32, 0.8)', // Goldenrod glow
    intensity: 0.75,
    vibrance: 0.8
  },

  pain: {
    primary: 'rgb(220, 20, 60)', // Crimson
    secondary: 'rgb(255, 69, 0)', // Orange red
    accent: 'rgb(255, 99, 71)', // Tomato
    glow: 'rgba(220, 20, 60, 0.8)', // Crimson glow
    intensity: 0.8,
    vibrance: 0.85
  },

  shame: {
    primary: 'rgb(255, 105, 180)', // Hot pink
    secondary: 'rgb(255, 182, 193)', // Light pink
    accent: 'rgb(255, 228, 225)', // Misty rose
    glow: 'rgba(255, 105, 180, 0.6)', // Hot pink glow
    intensity: 0.6,
    vibrance: 0.7
  },

  sympathy: {
    primary: 'rgb(135, 206, 235)', // Sky blue
    secondary: 'rgb(173, 216, 230)', // Light blue
    accent: 'rgb(240, 248, 255)', // Alice blue
    glow: 'rgba(135, 206, 235, 0.8)', // Sky blue glow
    intensity: 0.8,
    vibrance: 0.85
  },

  tiredness: {
    primary: 'rgb(128, 128, 128)', // Gray
    secondary: 'rgb(169, 169, 169)', // Dark gray
    accent: 'rgb(192, 192, 192)', // Silver
    glow: 'rgba(128, 128, 128, 0.5)', // Gray glow
    intensity: 0.4,
    vibrance: 0.5
  }
};

/**
 * Blends multiple emotions based on their intensity values
 * Returns a single color scheme representing the dominant emotional state
 */
export function blendEmotionColors(emotions: Record<string, number>): EmotionColorScheme {
  // Default neutral state
  const defaultScheme: EmotionColorScheme = {
    primary: 'rgb(248, 248, 255)', // Ghost white
    secondary: 'rgb(245, 245, 220)', // Beige
    accent: 'rgb(255, 255, 240)', // Ivory
    glow: 'rgba(192, 192, 192, 0.1)', // Very soft silver glow
    intensity: 0.3,
    vibrance: 0.4
  };

  if (!emotions || Object.keys(emotions).length === 0) {
    return defaultScheme;
  }

  // Find the dominant emotion (highest value)
  let dominantEmotion = '';
  let maxIntensity = 0;

  for (const [emotion, value] of Object.entries(emotions)) {
    if (value > maxIntensity && HUME_EMOTION_COLORS[emotion]) {
      maxIntensity = value;
      dominantEmotion = emotion;
    }
  }

  if (!dominantEmotion || maxIntensity < 0.1) {
    return defaultScheme;
  }

  const dominantScheme = HUME_EMOTION_COLORS[dominantEmotion];

  // Blend with secondary emotions if they're significant
  const secondaryEmotions = Object.entries(emotions)
    .filter(([emotion, value]) => emotion !== dominantEmotion && value > 0.2 && HUME_EMOTION_COLORS[emotion])
    .sort(([, a], [, b]) => b - a)
    .slice(0, 2); // Take top 2 secondary emotions

  if (secondaryEmotions.length === 0) {
    return {
      ...dominantScheme,
      intensity: dominantScheme.intensity * maxIntensity,
      vibrance: dominantScheme.vibrance * maxIntensity
    };
  }

  // Simple blending - use dominant emotion as base, modulate with secondary
  const blendedScheme = { ...dominantScheme };

  // Adjust intensity and vibrance based on emotional complexity
  const totalSecondaryIntensity = secondaryEmotions.reduce((sum, [, value]) => sum + value, 0);
  blendedScheme.intensity = Math.min(1.0, dominantScheme.intensity * maxIntensity + totalSecondaryIntensity * 0.1);
  blendedScheme.vibrance = Math.min(1.0, dominantScheme.vibrance * maxIntensity + totalSecondaryIntensity * 0.05);

  return blendedScheme;
}

/**
 * Converts RGB string to individual color components
 */
export function parseRgbColor(rgbString: string): { r: number; g: number; b: number } {
  const match = rgbString.match(/rgb\((\d+),\s*(\d+),\s*(\d+)\)/);
  if (!match) {
    return { r: 248, g: 248, b: 255 }; // Default to ghost white
  }
  return {
    r: parseInt(match[1]),
    g: parseInt(match[2]),
    b: parseInt(match[3])
  };
}

/**
 * Generates a smooth background gradient based on emotion colors
 */
export function generateEmotionBackground(colorScheme: EmotionColorScheme): string {
  const primary = parseRgbColor(colorScheme.primary);
  const secondary = parseRgbColor(colorScheme.secondary);

  // Create a very subtle gradient for the background
  const alpha = 0.05 + (colorScheme.intensity * 0.1); // Very subtle

  return `linear-gradient(135deg,
    rgba(${primary.r}, ${primary.g}, ${primary.b}, ${alpha}) 0%,
    rgba(${secondary.r}, ${secondary.g}, ${secondary.b}, ${alpha * 0.7}) 50%,
    rgba(255, 255, 255, 1) 100%)`;
}

/**
 * Gets the appropriate orb colors based on emotion state
 */
export function getOrbColors(colorScheme: EmotionColorScheme): {
  primary: string;
  secondary: string;
  glow: string;
} {
  return {
    primary: colorScheme.primary,
    secondary: colorScheme.secondary,
    glow: colorScheme.glow
  };
}
